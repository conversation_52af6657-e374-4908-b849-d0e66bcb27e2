"""
应用配置CRUD操作
"""
from typing import Any, Dict, Optional, Union, List

from sqlalchemy.orm import Session

from app.crud.base import CRUDBaseWithApp
from app.models.app_config import AppConfig, AppConfigHistory
from app.schemas.app_config import AppConfigCreate, AppConfigUpdate


class CRUDAppConfig(CRUDBaseWithApp[AppConfig, AppConfigCreate, AppConfigUpdate]):
    """应用配置CRUD"""
    
    def get_by_key(self, db: Session, *, app_id: str, config_key: str) -> Optional[AppConfig]:
        """根据应用ID和配置key获取配置"""
        return db.query(AppConfig).filter(
            AppConfig.app_id == app_id,
            AppConfig.config_key == config_key
        ).first()

    def get_public_configs(self, db: Session, *, app_id: str) -> List[AppConfig]:
        """获取应用的公开配置"""
        return db.query(AppConfig).filter(
            AppConfig.app_id == app_id,
            AppConfig.is_public == 1,
            AppConfig.status == 1
        ).all()

    def get_configs_by_category(
        self, 
        db: Session, 
        *, 
        app_id: str, 
        category: str
    ) -> List[AppConfig]:
        """根据分类获取配置"""
        return db.query(AppConfig).filter(
            AppConfig.app_id == app_id,
            AppConfig.category == category,
            AppConfig.status == 1
        ).order_by(AppConfig.sort_order).all()

    def create_with_app(
        self, 
        db: Session, 
        *, 
        obj_in: AppConfigCreate, 
        app_id: str,
        created_by: int = None
    ) -> AppConfig:
        """创建应用配置"""
        db_obj = AppConfig(
            app_id=app_id,
            config_key=obj_in.config_key,
            config_value=obj_in.config_value,
            config_type=obj_in.config_type,
            description=obj_in.description,
            is_public=obj_in.is_public,
            is_encrypted=obj_in.is_encrypted,
            category=obj_in.category,
            sort_order=obj_in.sort_order,
            created_by=created_by
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # 创建变更历史
        self._create_config_history(
            db, 
            config=db_obj, 
            change_type="create",
            old_value=None,
            new_value=obj_in.config_value,
            changed_by=created_by
        )
        
        return db_obj

    def update_config(
        self,
        db: Session,
        *,
        app_id: str,
        config_key: str,
        obj_in: Union[AppConfigUpdate, Dict[str, Any]],
        updated_by: int = None
    ) -> Optional[AppConfig]:
        """更新配置"""
        db_obj = self.get_by_key(db, app_id=app_id, config_key=config_key)
        if not db_obj:
            return None
        
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        
        old_value = db_obj.config_value
        
        # 更新版本号
        if "config_value" in update_data:
            db_obj.version += 1
        
        # 更新字段
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        db_obj.updated_by = updated_by
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # 创建变更历史
        if "config_value" in update_data:
            self._create_config_history(
                db,
                config=db_obj,
                change_type="update",
                old_value=old_value,
                new_value=update_data["config_value"],
                changed_by=updated_by
            )
        
        return db_obj

    def delete_config(
        self,
        db: Session,
        *,
        app_id: str,
        config_key: str,
        deleted_by: int = None
    ) -> Optional[AppConfig]:
        """删除配置"""
        db_obj = self.get_by_key(db, app_id=app_id, config_key=config_key)
        if not db_obj:
            return None
        
        old_value = db_obj.config_value
        
        # 创建变更历史
        self._create_config_history(
            db,
            config=db_obj,
            change_type="delete",
            old_value=old_value,
            new_value=None,
            changed_by=deleted_by
        )
        
        db.delete(db_obj)
        db.commit()
        return db_obj

    def batch_update_configs(
        self,
        db: Session,
        *,
        app_id: str,
        configs: List[Dict[str, Any]],
        updated_by: int = None
    ) -> List[AppConfig]:
        """批量更新配置"""
        updated_configs = []
        
        for config_data in configs:
            config_key = config_data.get("config_key")
            if not config_key:
                continue
            
            config = self.update_config(
                db,
                app_id=app_id,
                config_key=config_key,
                obj_in=config_data,
                updated_by=updated_by
            )
            if config:
                updated_configs.append(config)
        
        return updated_configs

    def rollback_to_version(
        self,
        db: Session,
        *,
        app_id: str,
        config_key: str,
        version: int,
        rolled_back_by: int = None
    ) -> Optional[AppConfig]:
        """回滚到指定版本"""
        # 获取指定版本的历史记录
        history = db.query(AppConfigHistory).filter(
            AppConfigHistory.app_id == app_id,
            AppConfigHistory.config_key == config_key,
            AppConfigHistory.version == version
        ).first()
        
        if not history:
            return None
        
        # 更新当前配置
        current_config = self.get_by_key(db, app_id=app_id, config_key=config_key)
        if not current_config:
            return None
        
        old_value = current_config.config_value
        current_config.config_value = history.new_value
        current_config.version += 1
        current_config.updated_by = rolled_back_by
        
        db.add(current_config)
        db.commit()
        db.refresh(current_config)
        
        # 创建回滚历史记录
        self._create_config_history(
            db,
            config=current_config,
            change_type="rollback",
            old_value=old_value,
            new_value=history.new_value,
            changed_by=rolled_back_by,
            change_reason=f"回滚到版本 {version}"
        )
        
        return current_config

    def _create_config_history(
        self,
        db: Session,
        *,
        config: AppConfig,
        change_type: str,
        old_value: str = None,
        new_value: str = None,
        changed_by: int = None,
        change_reason: str = None
    ) -> AppConfigHistory:
        """创建配置变更历史"""
        history = AppConfigHistory(
            config_id=config.id,
            app_id=config.app_id,
            config_key=config.config_key,
            old_value=old_value,
            new_value=new_value,
            change_type=change_type,
            version=config.version,
            changed_by=changed_by,
            change_reason=change_reason
        )
        db.add(history)
        db.commit()
        db.refresh(history)
        return history


class CRUDAppConfigHistory(CRUDBaseWithApp[AppConfigHistory, Dict, Dict]):
    """应用配置历史CRUD"""
    
    def get_config_history(
        self,
        db: Session,
        *,
        app_id: str,
        config_key: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[AppConfigHistory]:
        """获取配置变更历史"""
        return db.query(AppConfigHistory).filter(
            AppConfigHistory.app_id == app_id,
            AppConfigHistory.config_key == config_key
        ).order_by(AppConfigHistory.created_at.desc()).offset(skip).limit(limit).all()


# 创建CRUD实例
app_config = CRUDAppConfig(AppConfig)
app_config_history = CRUDAppConfigHistory(AppConfigHistory)
