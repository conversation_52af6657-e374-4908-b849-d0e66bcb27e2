{"name": "multi-app-mini", "version": "1.0.0", "description": "多应用管理平台 - 小程序端", "private": true, "scripts": {"build:mp-weixin": "uni build -p mp-weixin", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-qq": "uni build -p mp-qq", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-qq": "uni -p mp-qq"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-alpha-3090920230601001", "@dcloudio/uni-components": "^3.0.0-alpha-3090920230601001", "@dcloudio/uni-mp-vue": "^3.0.0-alpha-3090920230601001", "vue": "^3.3.8", "pinia": "^2.1.7"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-cli-shared": "^3.0.0-alpha-3090920230601001", "@dcloudio/vite-plugin-uni": "^3.0.0-alpha-3090920230601001", "typescript": "^5.2.2", "vite": "^4.4.9"}}