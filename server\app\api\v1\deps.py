"""
API依赖注入
"""
from typing import Generator, Optional

from fastapi import Depends, HTTPException, Header, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from sqlalchemy.orm import Session

from app.core import security
from app.core.config import settings
from app.db.session import get_db
from app.models.application import Application
from app.models.admin_user import AdminUser
from app.models.app_user import AppUser
from app import crud

security_scheme = HTTPBearer()


def get_current_admin_user(
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme)
) -> AdminUser:
    """获取当前管理员用户"""
    try:
        payload = jwt.decode(
            credentials.credentials, 
            settings.SECRET_KEY, 
            algorithms=[security.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = crud.admin_user.get(db, id=user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="用户不存在"
        )
    return user


def get_current_app(
    x_app_id: str = Header(..., description="应用唯一标识ID"),
    db: Session = Depends(get_db)
) -> Application:
    """获取当前应用"""
    app = crud.application.get_by_app_id(db, app_id=x_app_id)
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在或已禁用"
        )
    if app.status != 1:  # 假设1表示启用状态
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="应用已禁用"
        )
    return app


def get_current_app_user(
    current_app: Application = Depends(get_current_app),
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme),
    db: Session = Depends(get_db)
) -> AppUser:
    """获取当前应用用户"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[security.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        app_id: str = payload.get("app_id")
        
        if user_id is None or app_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
            
        # 验证token中的app_id与header中的app_id是否一致
        if app_id != current_app.app_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="应用ID不匹配"
            )
            
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = crud.app_user.get_by_app_and_user_id(
        db, app_id=current_app.app_id, user_id=user_id
    )
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


def get_current_active_admin_user(
    current_user: AdminUser = Depends(get_current_admin_user),
) -> AdminUser:
    """获取当前活跃的管理员用户"""
    if current_user.status != 1:  # 假设1表示活跃状态
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="用户已被禁用"
        )
    return current_user


def get_current_active_app_user(
    current_user: AppUser = Depends(get_current_app_user),
) -> AppUser:
    """获取当前活跃的应用用户"""
    if current_user.status != 1:  # 假设1表示活跃状态
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    return current_user
