"""
API依赖注入
"""
from typing import Optional

from fastapi import Depends, HTTPException, Header, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from sqlalchemy.orm import Session

from app.core import security
from app.core.config import settings
from app.db.session import get_db
from app.models.application import Application
from app.models.admin_user import AdminUser
from app.models.app_user import AppUser
from app import crud

security_scheme = HTTPBearer()


def get_current_request_info(request: Request) -> dict:
    """获取当前请求信息"""
    return {
        "ip": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "method": request.method,
        "url": str(request.url),
        "headers": dict(request.headers)
    }


def get_current_admin_user(
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme)
) -> AdminUser:
    """获取当前管理员用户"""
    try:
        payload = jwt.decode(
            credentials.credentials, 
            settings.SECRET_KEY, 
            algorithms=[security.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = crud.admin_user.get(db, id=user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail="用户不存在"
        )
    return user


def get_current_app(
    x_app_id: str = Header(..., description="应用唯一标识ID"),
    db: Session = Depends(get_db)
) -> Application:
    """获取当前应用"""
    app = crud.application.get_by_app_id(db, app_id=x_app_id)
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在或已禁用"
        )
    if app.status != 1:  # 假设1表示启用状态
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="应用已禁用"
        )
    return app


def get_current_app_user(
    current_app: Application = Depends(get_current_app),
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme),
    db: Session = Depends(get_db)
) -> AppUser:
    """获取当前应用用户"""
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[security.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        app_id: str = payload.get("app_id")
        
        if user_id is None or app_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
            
        # 验证token中的app_id与header中的app_id是否一致
        if app_id != current_app.app_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="应用ID不匹配"
            )
            
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = crud.app_user.get_by_app_and_user_id(
        db, app_id=current_app.app_id, user_id=user_id
    )
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


def get_current_active_admin_user(
    current_user: AdminUser = Depends(get_current_admin_user),
) -> AdminUser:
    """获取当前活跃的管理员用户"""
    if current_user.status != 1:  # 假设1表示活跃状态
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail="用户已被禁用"
        )
    return current_user


def get_current_active_app_user(
    current_user: AppUser = Depends(get_current_app_user),
) -> AppUser:
    """获取当前活跃的应用用户"""
    if current_user.status != 1:  # 假设1表示活跃状态
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    return current_user


def get_optional_current_admin_user(
    db: Session = Depends(get_db),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_scheme)
) -> Optional[AdminUser]:
    """获取可选的当前管理员用户（用于可选认证的接口）"""
    if not credentials:
        return None

    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[security.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
    except JWTError:
        return None

    user = crud.admin_user.get(db, id=user_id)
    return user


def get_optional_current_app_user(
    current_app: Application = Depends(get_current_app),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security_scheme),
    db: Session = Depends(get_db)
) -> Optional[AppUser]:
    """获取可选的当前应用用户（用于可选认证的接口）"""
    if not credentials:
        return None

    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.SECRET_KEY,
            algorithms=[security.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        app_id: str = payload.get("app_id")

        if user_id is None or app_id is None:
            return None

        # 验证token中的app_id与header中的app_id是否一致
        if app_id != current_app.app_id:
            return None

    except JWTError:
        return None

    user = crud.app_user.get_by_app_and_user_id(
        db, app_id=current_app.app_id, user_id=user_id
    )
    return user


def verify_admin_permission(
    permission: str,
    current_user: AdminUser = Depends(get_current_active_admin_user),
    db: Session = Depends(get_db)
) -> AdminUser:
    """验证管理员权限"""
    # 这里可以根据实际需求实现权限验证逻辑
    # 例如检查用户角色是否有指定权限
    user_menus = crud.admin_menu.get_user_menus(db, user_id=current_user.id)
    user_permissions = [menu.permission for menu in user_menus if menu.permission]

    if permission not in user_permissions and not crud.admin_user.is_superuser(current_user):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    return current_user


def get_pagination_params(
    page: int = 1,
    page_size: int = 20
) -> dict:
    """获取分页参数"""
    if page < 1:
        page = 1
    if page_size < 1 or page_size > 100:
        page_size = 20

    return {
        "page": page,
        "page_size": page_size,
        "skip": (page - 1) * page_size,
        "limit": page_size
    }
