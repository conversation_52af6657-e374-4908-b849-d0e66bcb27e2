import { request } from '@/utils/request'
import type { LoginForm, LoginResponse, AdminUser, ChangePasswordForm } from '@/types/auth'

// 登录
export const login = (data: LoginForm): Promise<LoginResponse> => {
  const formData = new FormData()
  formData.append('username', data.username)
  formData.append('password', data.password)
  
  return request.post('/admin/login', formData, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 登出
export const logout = (): Promise<any> => {
  return request.post('/admin/logout')
}

// 获取当前用户信息
export const getUserInfo = (): Promise<AdminUser> => {
  return request.get('/admin/me')
}

// 更新当前用户信息
export const updateUserInfo = (data: Partial<AdminUser>): Promise<AdminUser> => {
  return request.put('/admin/me', data)
}

// 修改密码
export const changePassword = (data: ChangePasswordForm): Promise<any> => {
  return request.post('/admin/change-password', {
    old_password: data.old_password,
    new_password: data.new_password
  })
}
