"""
数据库模型汇总
"""
from app.models.base import Base, BaseModel
from app.models.admin_user import Admin<PERSON>ser, AdminRole, AdminMenu, AdminRoleMenu
from app.models.application import Application
from app.models.app_user import AppUser, UserLoginHistory
from app.models.app_config import AppConfig, AppConfigHistory
from app.models.log import OperationLog, ApiAccessLog, SystemLog
from app.models.alarm import AlarmRule, AlarmRecord, NotificationTemplate, NotificationLog

# 导出所有模型，供Alembic使用
__all__ = [
    "Base",
    "BaseModel",
    "AdminUser",
    "AdminRole", 
    "AdminMenu",
    "AdminRoleMenu",
    "Application",
    "AppUser",
    "UserLoginHistory",
    "AppConfig",
    "AppConfigHistory",
    "OperationLog",
    "ApiAccessLog",
    "SystemLog",
    "AlarmRule",
    "AlarmRecord",
    "NotificationTemplate",
    "NotificationLog",
]
