"""
应用配置Schema
"""
from typing import Optional, List, Any
from pydantic import BaseModel

from app.schemas.base import BaseSchema, TimestampMixin


class AppConfigBase(BaseModel):
    """应用配置基础Schema"""
    config_key: str
    config_value: Optional[str] = None
    config_type: Optional[str] = "string"
    description: Optional[str] = None
    is_public: Optional[int] = 0
    is_encrypted: Optional[int] = 0
    category: Optional[str] = None
    sort_order: Optional[int] = 0


class AppConfigCreate(AppConfigBase):
    """创建应用配置Schema"""
    app_id: str  # 必须包含app_id


class AppConfigUpdate(BaseModel):
    """更新应用配置Schema"""
    config_value: Optional[str] = None
    config_type: Optional[str] = None
    description: Optional[str] = None
    is_public: Optional[int] = None
    is_encrypted: Optional[int] = None
    status: Optional[int] = None
    category: Optional[str] = None
    sort_order: Optional[int] = None


class AppConfigInDB(AppConfigBase, TimestampMixin, BaseSchema):
    """数据库中的应用配置Schema"""
    id: int
    app_id: str
    version: int
    status: int


class AppConfig(AppConfigBase, TimestampMixin, BaseSchema):
    """应用配置响应Schema"""
    id: int
    app_id: str
    version: int
    status: int


class AppConfigPublic(BaseModel):
    """公开的应用配置Schema（供应用端拉取）"""
    config_key: str
    config_value: Optional[str] = None
    config_type: str
    version: int


class AppConfigBatchUpdate(BaseModel):
    """批量更新应用配置Schema"""
    configs: List[dict]
    change_reason: Optional[str] = None


class AppConfigImport(BaseModel):
    """导入应用配置Schema"""
    configs: List[dict]
    overwrite: bool = False
    change_reason: Optional[str] = None


# 配置变更历史Schema
class AppConfigHistoryBase(BaseModel):
    """应用配置变更历史基础Schema"""
    config_key: str
    old_value: Optional[str] = None
    new_value: Optional[str] = None
    change_type: str  # create/update/delete
    version: int
    changed_by: int
    change_reason: Optional[str] = None


class AppConfigHistory(AppConfigHistoryBase, TimestampMixin, BaseSchema):
    """应用配置变更历史响应Schema"""
    id: int
    config_id: int
    app_id: str


class AppConfigRollback(BaseModel):
    """配置回滚Schema"""
    version: int
    change_reason: Optional[str] = None
