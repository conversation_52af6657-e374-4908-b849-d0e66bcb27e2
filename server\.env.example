# 项目配置
PROJECT_NAME=多应用管理与监控平台
VERSION=1.0.0

# 安全配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=multi_app_platform
POSTGRES_PORT=5432

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 邮件配置
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=多应用管理平台

# 超级管理员配置
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123

# 其他配置
USERS_OPEN_REGISTRATION=false
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]

# 监控配置
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8001

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
