# 多应用集中管理与监控平台 - 开发指导文档

本文档基于《项目设计文档》，结合最新项目结构（server/admin/apps），为开发者（包括AI编程助手）提供清晰、可执行的开发步骤和指导。**本平台以"集中管理多应用"为核心目标，强调统一支撑、简洁后台和高效服务器规划。**

---

## Ⅰ. 项目结构与开发环境搭建

### 1. 项目目录结构规划

```
.
├── server/           # 后端服务，统一支撑所有应用（核心）
│   ├── app/
│   │   ├── api/         # API 路由层，按版本组织，如 v1/
│   │   │   ├── v1/
│   │   │   │   ├── endpoints/   # 具体业务接口
│   │   │   │   └── deps.py      # 依赖注入
│   │   ├── core/        # 全局配置、安全、日志等
│   │   ├── crud/        # 数据库操作，所有与应用相关的操作均需 app_id 过滤
│   │   ├── db/          # 数据库连接、会话管理、基类
│   │   ├── models/      # SQLAlchemy 数据模型，所有表与 app_id 关联
│   │   ├── schemas/     # Pydantic 校验模型，含 app_id 字段
│   │   └── main.py      # FastAPI 应用主入口
│   ├── tests/           # 单元测试和集成测试
│   ├── alembic/         # 数据库迁移脚本
│   ├── requirements.txt
│   └── Dockerfile
├── admin/            # 管理后台前端（Vue3/Element Plus），简洁实用为主
│   ├── src/
│   │   ├── api/         # 封装所有后端 API 请求
│   │   ├── assets/      # 静态资源
│   │   ├── components/  # 全局可复用组件
│   │   ├── router/      # 路由配置
│   │   ├── store/       # Pinia 状态管理
│   │   ├── utils/       # 工具函数
│   │   └── views/       # 页面级组件（以应用管理、用户管理、配置管理、监控为主，避免复杂化）
│   ├── public/
│   ├── package.json
│   └── Dockerfile
├── apps/             # 各类应用端（H5、小程序、APP等），结构可参考 admin
│   ├── h5/
│   ├── mini/
│   └── app/
├── design/           # 设计与规范文档
│   ├── 项目设计.md      # 项目整体架构和技术方案设计文档
│   ├── 开发所用的指导文档.md  # 当前文件，开发者操作指南
│   └── 开发规则.md      # 编码规范、Git使用、目录命名等开发必须遵守的规则
├── docker-compose.yml # 多容器Docker编排文件，定义整个系统的部署方式
└── .gitignore         # Git版本控制忽略配置
```

#### 目录结构说明
- `server/`：平台统一后端，所有应用的 API、认证、配置、监控等均在此集中管理和支撑。包含核心框架 FastAPI 的实现代码。
- `admin/`：管理后台前端，采用 Vue3 + Element Plus 技术栈，实现应用管理、用户管理、配置管理等核心功能。
- `apps/`：各类客户端应用的存放位置，目前包括 H5 移动端、微信小程序和原生 APP，每个子目录下是一个完整的客户端项目。
- `design/`：设计与规范文档目录，包含项目的架构设计文档、开发规则文档和当前的开发指导文档。
- `docker-compose.yml`：Docker 编排文件，用于定义和运行多容器 Docker 应用程序。
- `.gitignore`：Git 版本控制系统忽略文件配置，确保不必要的文件不被提交到仓库。

---

## Ⅱ. 服务器层多应用集中管理策略（核心）

### 1. 设计原则
- **集中管理多应用**：平台作为所有应用的统一支撑主体，所有业务、数据、配置、监控等均以 app_id 为核心进行集中管理。
- **统一 API 入口**：管理端与应用端接口分离，前缀规范，便于权限与路由管理。
- **依赖注入与权限隔离**：利用 FastAPI 依赖注入机制，自动校验 app_id，实现权限和数据隔离。
- **数据库强隔离**：所有与应用相关的数据表和 CRUD 操作均强制 app_id 过滤，防止数据串用。
- **可扩展性与可观测性**：配置、监控、日志等均以 app_id 作为标签，便于后续扩展和运维。
- **实际可维护性**：避免多余抽象，所有设计以实际落地和易维护为目标。

---

### 2. API 入口与路由规范
- **Admin API**: 管理后台接口，前缀 `/api/v1/admin/...`，仅供平台管理员使用。
- **App API**: 各应用端接口，前缀 `/api/v1/apps/...`，所有请求需通过 HTTP Header `X-App-ID` 传递应用标识。

---

### 3. 身份认证与授权
- **管理员认证**: 用户名+密码登录，JWT 中包含 user_id 和 role。
- **应用用户认证**: 手机号、微信等方式登录，JWT 中包含 user_id 和 app_id。
- **权限分离**: 管理员与应用用户权限体系完全分离，避免混淆。

---

### 4. 依赖注入与数据隔离实现
- 在 `server/app/api/v1/deps.py` 中定义依赖项，自动校验 X-App-ID 并获取当前应用信息。
- API 路由只需声明依赖即可自动完成应用身份校验和隔离。
- 所有与应用相关的数据库操作（CRUD）都必须以 app_id 作为强制参数。

**依赖注入校验 app_id 示例：**
```python
async def get_current_app(
    x_app_id: Annotated[str, Header(description="应用唯一标识ID")],
    db: Session = Depends(get_db)
) -> Application:
    # 校验 app_id 合法性，返回当前应用对象
    ...
```

**API 路由中使用：**
```python
@router.get("/items")
async def read_app_items(
    current_app: Application = Depends(get_current_app)
):
    # 业务逻辑自动隔离在当前 app_id 下
    ...
```

**CRUD 层强制 app_id：**
```python
def get_multi_by_app(self, db: Session, *, app_id: str, ...):
    return db.query(self.model).filter(Item.app_id == app_id).all()
```

---

### 5. 服务器端合理规划建议
- **API 设计**：所有与应用相关的接口和数据表均需支持 app_id 隔离，接口风格统一，便于前后端协作。
- **配置与监控**：应用级配置、监控、日志等均以 app_id 作为标签，便于后续扩展和运维。
- **测试与文档**：核心业务逻辑和API接口需有单元测试和集成测试，API或模型变更需及时同步文档。
- **易维护性**：目录结构清晰，代码分层明确，避免过度抽象，便于新成员快速上手。

---

## Ⅲ. 核心功能开发步骤

请按照以下模块顺序进行开发。每个模块内，遵循 **"数据库模型 -> 校验模型 -> 业务逻辑(CRUD) -> API路由 -> 前端页面"** 的顺序。

### 阶段一：基础框架与用户体系

#### 步骤 1: 数据库模型 (Models)
- **位置**: `server/app/models/`
- **任务**:
    1.  创建 `user.py`、`role.py`、`user_role.py`、`application.py`，并确保 `application` 具备 `app_id`、`app_secret` 字段。
    2.  其他模型如 `config.py`、`log.py` 等均需与 `app_id` 关联。

#### 步骤 2: API 校验模型 (Schemas)
- **位置**: `server/app/schemas/`
- **任务**:
    1.  为每个数据模型创建对应的 Pydantic Schema。
    2.  所有与应用相关的Schema需包含 `app_id` 字段。

#### 步骤 3: 业务逻辑 (CRUD)
- **位置**: `server/app/crud/`
- **任务**:
    1.  编写针对每个数据模型的增删改查函数。
    2.  所有操作需基于 `app_id` 进行数据隔离。

#### 步骤 4: API 路由 (API)
- **位置**: `server/app/api/`
- **任务**:
    1.  用户认证、应用管理、用户管理、配置管理、日志上报等接口，均需支持多应用接入（带 `app_id` 参数）。
    2.  管理端和各应用端均通过API与server交互。

#### 步骤 5: 前端实现
- **admin/**：管理后台，开发应用管理、用户管理、配置管理、监控等页面，功能以"够用"为主，避免复杂化。
- **apps/**：各应用端独立开发，需通过API与server交互，所有请求需带 `app_id`。

---

## Ⅳ. 开发要求

- **遵循规范**: 严格遵守 `开发规则.md` 中定义的各项规范。
- **多应用集中管理**: server端所有与业务相关的接口和数据表均需支持多应用隔离（基于 `app_id`）。
- **提交代码**: 遵循 Git 提交规范。
- **编写测试**: 对核心业务逻辑和API接口编写单元测试和集成测试。
- **文档同步**: 如果API或模型有变更，及时更新相关文档。
- **AI协作**: 向AI助手提问时，请提供清晰的上下文。

---
本指导文档将随项目进展持续更新。 