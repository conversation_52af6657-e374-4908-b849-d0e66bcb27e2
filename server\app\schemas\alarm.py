"""
告警相关Schema
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel

from app.schemas.base import BaseSchema, TimestampMixin


# 告警规则Schema
class AlarmRuleBase(BaseModel):
    """告警规则基础Schema"""
    name: str
    app_id: Optional[str] = None  # 为空表示全局规则
    metric_name: str
    metric_type: str
    condition: str  # gt/lt/eq/gte/lte
    threshold: float
    duration: Optional[int] = 300
    severity: Optional[str] = "warning"
    status: Optional[int] = 1
    notify_channels: Optional[str] = None
    notify_users: Optional[str] = None
    description: Optional[str] = None


class AlarmRuleCreate(AlarmRuleBase):
    """创建告警规则Schema"""
    pass


class AlarmRuleUpdate(BaseModel):
    """更新告警规则Schema"""
    name: Optional[str] = None
    metric_name: Optional[str] = None
    metric_type: Optional[str] = None
    condition: Optional[str] = None
    threshold: Optional[float] = None
    duration: Optional[int] = None
    severity: Optional[str] = None
    status: Optional[int] = None
    notify_channels: Optional[str] = None
    notify_users: Optional[str] = None
    description: Optional[str] = None


class AlarmRule(AlarmRuleBase, TimestampMixin, BaseSchema):
    """告警规则响应Schema"""
    id: int


# 告警记录Schema
class AlarmRecordBase(BaseModel):
    """告警记录基础Schema"""
    rule_id: int
    app_id: Optional[str] = None
    metric_name: str
    metric_value: float
    threshold: float
    severity: str
    title: str
    content: Optional[str] = None


class AlarmRecordCreate(AlarmRecordBase):
    """创建告警记录Schema"""
    pass


class AlarmRecordUpdate(BaseModel):
    """更新告警记录Schema"""
    status: Optional[int] = None
    handler_id: Optional[int] = None
    handle_remark: Optional[str] = None


class AlarmRecord(AlarmRecordBase, TimestampMixin, BaseSchema):
    """告警记录响应Schema"""
    id: int
    status: int
    notify_status: int
    notify_channels: Optional[str] = None
    notify_detail: Optional[str] = None
    handler_id: Optional[int] = None
    handled_at: Optional[datetime] = None
    handle_remark: Optional[str] = None
    resolved_at: Optional[datetime] = None


# 通知模板Schema
class NotificationTemplateBase(BaseModel):
    """通知模板基础Schema"""
    name: str
    template_type: str  # email/sms/webhook
    subject: Optional[str] = None
    content: str
    variables: Optional[str] = None
    status: Optional[int] = 1
    description: Optional[str] = None


class NotificationTemplateCreate(NotificationTemplateBase):
    """创建通知模板Schema"""
    pass


class NotificationTemplateUpdate(BaseModel):
    """更新通知模板Schema"""
    name: Optional[str] = None
    subject: Optional[str] = None
    content: Optional[str] = None
    variables: Optional[str] = None
    status: Optional[int] = None
    description: Optional[str] = None


class NotificationTemplate(NotificationTemplateBase, TimestampMixin, BaseSchema):
    """通知模板响应Schema"""
    id: int


# 通知发送日志Schema
class NotificationLogBase(BaseModel):
    """通知发送日志基础Schema"""
    alarm_record_id: Optional[int] = None
    template_id: Optional[int] = None
    channel: str
    recipient: str
    subject: Optional[str] = None
    content: str
    status: int
    error_message: Optional[str] = None


class NotificationLog(NotificationLogBase, TimestampMixin, BaseSchema):
    """通知发送日志响应Schema"""
    id: int
    sent_at: Optional[datetime] = None
    response_detail: Optional[str] = None


# 告警统计Schema
class AlarmStats(BaseModel):
    """告警统计Schema"""
    total_count: int
    unhandled_count: int
    handled_count: int
    resolved_count: int
    critical_count: int
    error_count: int
    warning_count: int
    info_count: int
    today_count: int
    yesterday_count: int
    week_count: int
    month_count: int


# 告警处理请求Schema
class AlarmHandleRequest(BaseModel):
    """告警处理请求Schema"""
    alarm_ids: List[int]
    action: str  # confirm/handle/ignore/resolve
    remark: Optional[str] = None
