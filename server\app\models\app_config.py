"""
应用配置模型
"""
from sqlalchemy import Column, Integer, String, Text, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class AppConfig(BaseModel):
    """应用配置表"""
    __tablename__ = "app_configs"
    
    app_id = Column(String(50), ForeignKey("applications.app_id"), nullable=False, index=True, comment="所属应用ID")
    config_key = Column(String(100), nullable=False, index=True, comment="配置项key")
    config_value = Column(Text, nullable=True, comment="配置项value")
    config_type = Column(String(20), default="string", nullable=False, comment="配置类型：string/number/boolean/json")
    description = Column(Text, nullable=True, comment="配置项说明")
    is_public = Column(Integer, default=0, nullable=False, comment="是否公开：1-是，0-否")
    is_encrypted = Column(Integer, default=0, nullable=False, comment="是否加密：1-是，0-否")
    version = Column(Integer, default=1, nullable=False, comment="版本号")
    status = Column(Integer, default=1, nullable=False, comment="状态：1-启用，0-禁用")
    category = Column(String(50), nullable=True, comment="配置分类")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序")
    
    # 关联关系
    application = relationship("Application", back_populates="configs")
    history = relationship("AppConfigHistory", back_populates="config")


class AppConfigHistory(BaseModel):
    """应用配置变更历史表"""
    __tablename__ = "app_config_history"
    
    config_id = Column(Integer, ForeignKey("app_configs.id"), nullable=False, index=True, comment="配置项ID")
    app_id = Column(String(50), ForeignKey("applications.app_id"), nullable=False, index=True, comment="应用ID")
    config_key = Column(String(100), nullable=False, comment="配置项key")
    old_value = Column(Text, nullable=True, comment="旧值")
    new_value = Column(Text, nullable=True, comment="新值")
    change_type = Column(String(20), nullable=False, comment="变更类型：create/update/delete")
    version = Column(Integer, nullable=False, comment="版本号")
    changed_by = Column(Integer, nullable=False, comment="操作人ID")
    change_reason = Column(Text, nullable=True, comment="变更原因")
    
    # 关联关系
    config = relationship("AppConfig", back_populates="history")
