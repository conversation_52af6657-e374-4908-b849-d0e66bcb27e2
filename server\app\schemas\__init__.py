"""
Schema汇总
"""
from app.schemas.base import BaseSchema, TimestampMixin, ResponseBase, PaginationParams, PaginationResponse
from app.schemas.admin_user import (
    AdminUser, AdminUserCreate, AdminUserUpdate, AdminUserInDB,
    AdminRole, AdminRoleCreate, AdminRoleUpdate,
    AdminMenu, AdminMenuCreate, AdminMenuUpdate,
    AdminLoginRequest, AdminLoginResponse, AdminChangePasswordRequest
)
from app.schemas.application import (
    Application, ApplicationCreate, ApplicationUpdate, ApplicationInDB,
    ApplicationWithSecret, ApplicationStats
)
from app.schemas.app_user import (
    AppUser, AppUserCreate, AppUserUpdate, AppUserInDB,
    AppUserLoginRequest, AppUserRegisterRequest, AppUserLoginResponse,
    AppUserChangePasswordRequest, AppUserResetPasswordRequest,
    UserLoginHistory
)
from app.schemas.app_config import (
    AppConfig, AppConfigCreate, AppConfigUpdate, AppConfigInDB,
    AppConfigPublic, AppConfigBatchUpdate, AppConfigImport,
    AppConfigHistory, AppConfigRollback
)
from app.schemas.log import (
    OperationLog, OperationLogCreate,
    ApiAccessLog, ApiAccessLogCreate,
    SystemLog, SystemLogCreate,
    LogQueryParams, LogStats
)
from app.schemas.alarm import (
    AlarmRule, AlarmRuleCreate, AlarmRuleUpdate,
    AlarmRecord, AlarmRecordCreate, AlarmRecordUpdate,
    NotificationTemplate, NotificationTemplateCreate, NotificationTemplateUpdate,
    NotificationLog, AlarmStats, AlarmHandleRequest
)

__all__ = [
    # Base
    "BaseSchema", "TimestampMixin", "ResponseBase", "PaginationParams", "PaginationResponse",
    
    # Admin User
    "AdminUser", "AdminUserCreate", "AdminUserUpdate", "AdminUserInDB",
    "AdminRole", "AdminRoleCreate", "AdminRoleUpdate", 
    "AdminMenu", "AdminMenuCreate", "AdminMenuUpdate",
    "AdminLoginRequest", "AdminLoginResponse", "AdminChangePasswordRequest",
    
    # Application
    "Application", "ApplicationCreate", "ApplicationUpdate", "ApplicationInDB",
    "ApplicationWithSecret", "ApplicationStats",
    
    # App User
    "AppUser", "AppUserCreate", "AppUserUpdate", "AppUserInDB",
    "AppUserLoginRequest", "AppUserRegisterRequest", "AppUserLoginResponse",
    "AppUserChangePasswordRequest", "AppUserResetPasswordRequest",
    "UserLoginHistory",
    
    # App Config
    "AppConfig", "AppConfigCreate", "AppConfigUpdate", "AppConfigInDB",
    "AppConfigPublic", "AppConfigBatchUpdate", "AppConfigImport",
    "AppConfigHistory", "AppConfigRollback",
    
    # Log
    "OperationLog", "OperationLogCreate",
    "ApiAccessLog", "ApiAccessLogCreate", 
    "SystemLog", "SystemLogCreate",
    "LogQueryParams", "LogStats",
    
    # Alarm
    "AlarmRule", "AlarmRuleCreate", "AlarmRuleUpdate",
    "AlarmRecord", "AlarmRecordCreate", "AlarmRecordUpdate",
    "NotificationTemplate", "NotificationTemplateCreate", "NotificationTemplateUpdate",
    "NotificationLog", "AlarmStats", "AlarmHandleRequest",
]
