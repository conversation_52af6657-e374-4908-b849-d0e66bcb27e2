"""
应用Schema
"""
from typing import Optional
from pydantic import BaseModel, EmailStr

from app.schemas.base import BaseSchema, TimestampMixin


class ApplicationBase(BaseModel):
    """应用基础Schema"""
    name: str
    app_type: str  # h5/mini/app
    description: Optional[str] = None
    status: Optional[int] = 1
    domain: Optional[str] = None
    callback_url: Optional[str] = None
    logo_url: Optional[str] = None
    contact_name: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[EmailStr] = None


class ApplicationCreate(ApplicationBase):
    """创建应用Schema"""
    pass


class ApplicationUpdate(BaseModel):
    """更新应用Schema"""
    name: Optional[str] = None
    app_type: Optional[str] = None
    description: Optional[str] = None
    status: Optional[int] = None
    domain: Optional[str] = None
    callback_url: Optional[str] = None
    logo_url: Optional[str] = None
    contact_name: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[EmailStr] = None


class ApplicationInDB(ApplicationBase, TimestampMixin, BaseSchema):
    """数据库中的应用Schema"""
    id: int
    app_id: str
    app_secret: str


class Application(ApplicationBase, TimestampMixin, BaseSchema):
    """应用响应Schema"""
    id: int
    app_id: str
    # 注意：不返回app_secret，保护敏感信息


class ApplicationWithSecret(Application):
    """包含密钥的应用Schema（仅在创建时返回）"""
    app_secret: str


class ApplicationStats(BaseModel):
    """应用统计Schema"""
    app_id: str
    app_name: str
    total_users: int
    active_users_today: int
    active_users_week: int
    active_users_month: int
    api_calls_today: int
    api_calls_week: int
    api_calls_month: int
    error_rate_today: float
    avg_response_time: float
