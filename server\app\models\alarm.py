"""
告警相关模型
"""
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Float
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class AlarmRule(BaseModel):
    """告警规则表"""
    __tablename__ = "alarm_rules"
    
    name = Column(String(100), nullable=False, comment="规则名称")
    app_id = Column(String(50), ForeignKey("applications.app_id"), nullable=True, index=True, comment="应用ID（为空表示全局规则）")
    metric_name = Column(String(100), nullable=False, comment="监控指标名称")
    metric_type = Column(String(50), nullable=False, comment="指标类型：api_error_rate/response_time/user_count等")
    condition = Column(String(20), nullable=False, comment="条件：gt/lt/eq/gte/lte")
    threshold = Column(Float, nullable=False, comment="阈值")
    duration = Column(Integer, default=300, nullable=False, comment="持续时间（秒）")
    severity = Column(String(20), default="warning", nullable=False, comment="严重级别：info/warning/error/critical")
    status = Column(Integer, default=1, nullable=False, comment="状态：1-启用，0-禁用")
    notify_channels = Column(Text, nullable=True, comment="通知渠道JSON：email/sms/webhook等")
    notify_users = Column(Text, nullable=True, comment="通知用户ID列表JSON")
    description = Column(Text, nullable=True, comment="规则描述")
    
    # 关联关系
    records = relationship("AlarmRecord", back_populates="rule")


class AlarmRecord(BaseModel):
    """告警记录表"""
    __tablename__ = "alarm_records"
    
    rule_id = Column(Integer, ForeignKey("alarm_rules.id"), nullable=False, index=True, comment="告警规则ID")
    app_id = Column(String(50), ForeignKey("applications.app_id"), nullable=True, index=True, comment="应用ID")
    metric_name = Column(String(100), nullable=False, comment="指标名称")
    metric_value = Column(Float, nullable=False, comment="指标值")
    threshold = Column(Float, nullable=False, comment="阈值")
    severity = Column(String(20), nullable=False, comment="严重级别")
    status = Column(Integer, default=0, nullable=False, comment="处理状态：0-未处理，1-已确认，2-已处理，3-已忽略")
    title = Column(String(200), nullable=False, comment="告警标题")
    content = Column(Text, nullable=True, comment="告警内容")
    notify_status = Column(Integer, default=0, nullable=False, comment="通知状态：0-未发送，1-已发送，2-发送失败")
    notify_channels = Column(Text, nullable=True, comment="实际通知渠道JSON")
    notify_detail = Column(Text, nullable=True, comment="通知详情JSON")
    handler_id = Column(Integer, nullable=True, comment="处理人ID")
    handled_at = Column(DateTime, nullable=True, comment="处理时间")
    handle_remark = Column(Text, nullable=True, comment="处理备注")
    resolved_at = Column(DateTime, nullable=True, comment="解决时间")
    
    # 关联关系
    rule = relationship("AlarmRule", back_populates="records")
    application = relationship("Application", back_populates="alarms")


class NotificationTemplate(BaseModel):
    """通知模板表"""
    __tablename__ = "notification_templates"
    
    name = Column(String(100), nullable=False, comment="模板名称")
    template_type = Column(String(20), nullable=False, comment="模板类型：email/sms/webhook")
    subject = Column(String(200), nullable=True, comment="主题（邮件用）")
    content = Column(Text, nullable=False, comment="内容模板")
    variables = Column(Text, nullable=True, comment="可用变量JSON")
    status = Column(Integer, default=1, nullable=False, comment="状态：1-启用，0-禁用")
    description = Column(Text, nullable=True, comment="模板描述")


class NotificationLog(BaseModel):
    """通知发送日志表"""
    __tablename__ = "notification_logs"
    
    alarm_record_id = Column(Integer, ForeignKey("alarm_records.id"), nullable=True, index=True, comment="告警记录ID")
    template_id = Column(Integer, ForeignKey("notification_templates.id"), nullable=True, comment="模板ID")
    channel = Column(String(20), nullable=False, comment="通知渠道：email/sms/webhook")
    recipient = Column(String(200), nullable=False, comment="接收者")
    subject = Column(String(200), nullable=True, comment="主题")
    content = Column(Text, nullable=False, comment="发送内容")
    status = Column(Integer, nullable=False, comment="发送状态：1-成功，0-失败")
    error_message = Column(Text, nullable=True, comment="错误信息")
    sent_at = Column(DateTime, nullable=True, comment="发送时间")
    response_detail = Column(Text, nullable=True, comment="响应详情JSON")
