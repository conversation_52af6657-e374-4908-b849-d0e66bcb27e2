{"name": "multi-app-h5", "version": "1.0.0", "description": "多应用管理平台 - H5应用端", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vant": "^4.8.0", "axios": "^1.6.2", "@vant/touch-emulator": "^1.4.0", "dayjs": "^1.11.10", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "typescript": "^5.2.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.0", "vue-tsc": "^1.8.22", "postcss": "^8.4.32", "postcss-px-to-viewport": "^1.1.1"}}