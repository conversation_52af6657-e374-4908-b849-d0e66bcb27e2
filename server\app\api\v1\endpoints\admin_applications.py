"""
管理端应用管理API路由
"""
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud, schemas
from app.api.v1.deps import (
    get_db, 
    get_current_active_admin_user,
    get_pagination_params
)
from app.models.admin_user import AdminUser

router = APIRouter()


@router.get("/", response_model=List[schemas.Application])
def read_applications(
    db: Session = Depends(get_db),
    pagination: dict = Depends(get_pagination_params),
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """获取应用列表"""
    applications = crud.application.get_multi(
        db, skip=pagination["skip"], limit=pagination["limit"]
    )
    return applications


@router.post("/", response_model=schemas.ApplicationWithSecret)
def create_application(
    *,
    db: Session = Depends(get_db),
    app_in: schemas.ApplicationCreate,
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """创建应用"""
    # 检查应用名称是否已存在
    existing_app = crud.application.get_by_name(db, name=app_in.name)
    if existing_app:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="应用名称已存在"
        )
    
    application = crud.application.create(db, obj_in=app_in)
    return application


@router.get("/{app_id}", response_model=schemas.Application)
def read_application(
    *,
    db: Session = Depends(get_db),
    app_id: str,
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """获取应用详情"""
    application = crud.application.get_by_app_id(db, app_id=app_id)
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    return application


@router.put("/{app_id}", response_model=schemas.Application)
def update_application(
    *,
    db: Session = Depends(get_db),
    app_id: str,
    app_in: schemas.ApplicationUpdate,
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """更新应用"""
    application = crud.application.get_by_app_id(db, app_id=app_id)
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    application = crud.application.update(db, db_obj=application, obj_in=app_in)
    return application


@router.delete("/{app_id}")
def delete_application(
    *,
    db: Session = Depends(get_db),
    app_id: str,
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """删除应用"""
    application = crud.application.get_by_app_id(db, app_id=app_id)
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    # 检查是否有关联数据
    user_count = crud.app_user.count_by_app(db, app_id=app_id)
    if user_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"应用下还有 {user_count} 个用户，无法删除"
        )
    
    crud.application.remove(db, id=application.id)
    return {"message": "应用删除成功"}


@router.post("/{app_id}/regenerate-secret", response_model=schemas.ApplicationWithSecret)
def regenerate_application_secret(
    *,
    db: Session = Depends(get_db),
    app_id: str,
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """重新生成应用密钥"""
    application = crud.application.get_by_app_id(db, app_id=app_id)
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    application = crud.application.regenerate_secret(db, app=application)
    return application


@router.get("/{app_id}/stats", response_model=schemas.ApplicationStats)
def get_application_stats(
    *,
    db: Session = Depends(get_db),
    app_id: str,
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """获取应用统计信息"""
    application = crud.application.get_by_app_id(db, app_id=app_id)
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    stats = crud.application.get_app_stats(db, app_id=app_id)
    return stats
