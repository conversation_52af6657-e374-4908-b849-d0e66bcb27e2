"""
应用用户CRUD操作
"""
from typing import Any, Dict, Optional, Union, List
from datetime import datetime

from sqlalchemy.orm import Session

from app.core.security import get_password_hash, verify_password
from app.crud.base import CRUDBaseWithApp
from app.models.app_user import AppUser, UserLoginHistory
from app.schemas.app_user import AppUserCreate, AppUserUpdate


class CRUDAppUser(CRUDBaseWithApp[AppUser, AppUserCreate, AppUserUpdate]):
    """应用用户CRUD"""
    
    def get_by_username(self, db: Session, *, app_id: str, username: str) -> Optional[AppUser]:
        """根据应用ID和用户名获取用户"""
        return db.query(AppUser).filter(
            AppUser.app_id == app_id,
            AppUser.username == username
        ).first()

    def get_by_phone(self, db: Session, *, app_id: str, phone: str) -> Optional[AppUser]:
        """根据应用ID和手机号获取用户"""
        return db.query(AppUser).filter(
            AppUser.app_id == app_id,
            AppUser.phone == phone
        ).first()

    def get_by_email(self, db: Session, *, app_id: str, email: str) -> Optional[AppUser]:
        """根据应用ID和邮箱获取用户"""
        return db.query(AppUser).filter(
            AppUser.app_id == app_id,
            AppUser.email == email
        ).first()

    def get_by_app_and_user_id(self, db: Session, *, app_id: str, user_id: int) -> Optional[AppUser]:
        """根据应用ID和用户ID获取用户"""
        return db.query(AppUser).filter(
            AppUser.app_id == app_id,
            AppUser.id == user_id
        ).first()

    def create_with_app(self, db: Session, *, obj_in: AppUserCreate, app_id: str) -> AppUser:
        """创建应用用户"""
        password_hash = None
        if obj_in.password:
            password_hash = get_password_hash(obj_in.password)
        
        db_obj = AppUser(
            app_id=app_id,
            username=obj_in.username,
            password_hash=password_hash,
            phone=obj_in.phone,
            email=obj_in.email,
            nickname=obj_in.nickname,
            avatar_url=obj_in.avatar_url,
            gender=obj_in.gender,
            birthday=obj_in.birthday,
            status=obj_in.status,
            register_type=obj_in.register_type,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def authenticate(
        self, 
        db: Session, 
        *, 
        app_id: str, 
        username: str = None,
        phone: str = None,
        email: str = None,
        password: str
    ) -> Optional[AppUser]:
        """验证应用用户"""
        user = None
        if username:
            user = self.get_by_username(db, app_id=app_id, username=username)
        elif phone:
            user = self.get_by_phone(db, app_id=app_id, phone=phone)
        elif email:
            user = self.get_by_email(db, app_id=app_id, email=email)
        
        if not user or not user.password_hash:
            return None
        if not verify_password(password, user.password_hash):
            return None
        return user

    def is_active(self, user: AppUser) -> bool:
        """检查用户是否活跃"""
        return user.status == 1

    def update_password(self, db: Session, *, user: AppUser, new_password: str) -> AppUser:
        """更新密码"""
        user.password_hash = get_password_hash(new_password)
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def update_last_login(
        self, 
        db: Session, 
        *, 
        user: AppUser, 
        ip_address: str = None
    ) -> AppUser:
        """更新最后登录时间"""
        user.last_login_at = datetime.utcnow()
        user.last_login_ip = ip_address
        user.login_fail_count = 0  # 重置失败次数
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def increment_login_fail_count(self, db: Session, *, user: AppUser) -> AppUser:
        """增加登录失败次数"""
        user.login_fail_count += 1
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def get_active_users_by_app(self, db: Session, *, app_id: str) -> List[AppUser]:
        """获取应用的活跃用户"""
        return db.query(AppUser).filter(
            AppUser.app_id == app_id,
            AppUser.status == 1
        ).all()

    def search_users_by_app(
        self, 
        db: Session, 
        *, 
        app_id: str, 
        keyword: str = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AppUser]:
        """搜索应用用户"""
        query = db.query(AppUser).filter(AppUser.app_id == app_id)
        
        if keyword:
            query = query.filter(
                AppUser.username.contains(keyword) |
                AppUser.nickname.contains(keyword) |
                AppUser.phone.contains(keyword) |
                AppUser.email.contains(keyword)
            )
        
        return query.offset(skip).limit(limit).all()


class CRUDUserLoginHistory(CRUDBaseWithApp[UserLoginHistory, Dict, Dict]):
    """用户登录历史CRUD"""
    
    def create_login_record(
        self,
        db: Session,
        *,
        user_id: int,
        app_id: str,
        login_ip: str = None,
        user_agent: str = None,
        login_type: str = None,
        status: int,
        fail_reason: str = None
    ) -> UserLoginHistory:
        """创建登录记录"""
        db_obj = UserLoginHistory(
            user_id=user_id,
            app_id=app_id,
            login_ip=login_ip,
            user_agent=user_agent,
            login_type=login_type,
            status=status,
            fail_reason=fail_reason
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_user_login_history(
        self,
        db: Session,
        *,
        app_id: str,
        user_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[UserLoginHistory]:
        """获取用户登录历史"""
        return db.query(UserLoginHistory).filter(
            UserLoginHistory.app_id == app_id,
            UserLoginHistory.user_id == user_id
        ).order_by(UserLoginHistory.created_at.desc()).offset(skip).limit(limit).all()


# 创建CRUD实例
app_user = CRUDAppUser(AppUser)
user_login_history = CRUDUserLoginHistory(UserLoginHistory)
