// 登录表单
export interface LoginForm {
  username: string
  password: string
  captcha?: string
}

// 管理员用户信息
export interface AdminUser {
  id: number
  username: string
  email?: string
  real_name?: string
  phone?: string
  status: number
  role_id?: number
  last_login_at?: string
  login_fail_count: number
  locked_until?: string
  created_at: string
  updated_at: string
}

// 登录响应
export interface LoginResponse {
  access_token: string
  token_type: string
  user_info: AdminUser
}

// 修改密码表单
export interface ChangePasswordForm {
  old_password: string
  new_password: string
  confirm_password: string
}
