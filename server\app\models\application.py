"""
应用模型
"""
from sqlalchemy import Column, Integer, String, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class Application(BaseModel):
    """应用表"""
    __tablename__ = "applications"
    
    name = Column(String(100), nullable=False, comment="应用名称")
    app_id = Column(String(50), unique=True, index=True, nullable=False, comment="应用唯一标识")
    app_secret = Column(String(255), nullable=False, comment="应用密钥")
    app_type = Column(String(20), nullable=False, comment="应用类型：h5/mini/app")
    description = Column(Text, nullable=True, comment="应用描述")
    status = Column(Integer, default=1, nullable=False, comment="状态：1-正常，0-禁用")
    domain = Column(String(200), nullable=True, comment="应用域名")
    callback_url = Column(String(500), nullable=True, comment="回调地址")
    logo_url = Column(String(500), nullable=True, comment="应用Logo")
    contact_name = Column(String(50), nullable=True, comment="联系人姓名")
    contact_phone = Column(String(20), nullable=True, comment="联系人电话")
    contact_email = Column(String(100), nullable=True, comment="联系人邮箱")
    
    # 关联关系
    users = relationship("AppUser", back_populates="application")
    configs = relationship("AppConfig", back_populates="application")
    logs = relationship("ApiAccessLog", back_populates="application")
    alarms = relationship("AlarmRecord", back_populates="application")
