"""
管理端API路由
"""
from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app import crud, schemas
from app.api.v1.deps import (
    get_db,
    get_current_active_admin_user
)
from app.core import security
from app.core.config import settings
from app.models.admin_user import AdminUser

router = APIRouter()


@router.post("/login", response_model=schemas.AdminLoginResponse)
def login_admin(
    db: Session = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """管理员登录"""
    user = crud.admin_user.authenticate(
        db, username=form_data.username, password=form_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    elif not crud.admin_user.is_active(user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        user.id, expires_delta=access_token_expires
    )

    # 重置登录失败次数
    crud.admin_user.reset_login_fail_count(db, user=user)

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_info": user
    }


@router.post("/logout")
def logout_admin(
    _: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """管理员登出"""
    return {"message": "登出成功"}


@router.get("/me", response_model=schemas.AdminUser)
def read_admin_me(
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """获取当前管理员信息"""
    return current_user


@router.put("/me", response_model=schemas.AdminUser)
def update_admin_me(
    *,
    db: Session = Depends(get_db),
    user_in: schemas.AdminUserUpdate,
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """更新当前管理员信息"""
    user = crud.admin_user.update(db, db_obj=current_user, obj_in=user_in)
    return user


@router.post("/change-password")
def change_admin_password(
    *,
    db: Session = Depends(get_db),
    password_in: schemas.AdminChangePasswordRequest,
    current_user: AdminUser = Depends(get_current_active_admin_user)
) -> Any:
    """修改管理员密码"""
    if not security.verify_password(password_in.old_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="原密码错误"
        )

    crud.admin_user.update_password(db, user=current_user, new_password=password_in.new_password)
    return {"message": "密码修改成功"}


@router.get("/")
async def admin_root():
    """管理端根路径"""
    return {"message": "管理端API"}


@router.get("/health")
async def admin_health():
    """管理端健康检查"""
    return {"status": "healthy", "module": "admin"}
