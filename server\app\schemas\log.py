"""
日志相关Schema
"""
from typing import Optional
from pydantic import BaseModel

from app.schemas.base import BaseSchema, TimestampMixin


# 操作日志Schema
class OperationLogBase(BaseModel):
    """操作日志基础Schema"""
    user_id: int
    user_type: str  # admin/app_user
    app_id: Optional[str] = None
    action: str
    target_type: Optional[str] = None
    target_id: Optional[str] = None
    target_name: Optional[str] = None
    detail: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    status: int
    error_message: Optional[str] = None
    duration: Optional[int] = None


class OperationLogCreate(OperationLogBase):
    """创建操作日志Schema"""
    pass


class OperationLog(OperationLogBase, TimestampMixin, BaseSchema):
    """操作日志响应Schema"""
    id: int


# API访问日志Schema
class ApiAccessLogBase(BaseModel):
    """API访问日志基础Schema"""
    app_id: Optional[str] = None
    user_id: Optional[int] = None
    user_type: Optional[str] = None
    request_id: Optional[str] = None
    method: str
    path: str
    query_params: Optional[str] = None
    request_body: Optional[str] = None
    response_body: Optional[str] = None
    status_code: int
    latency: int
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    referer: Optional[str] = None
    error_message: Optional[str] = None


class ApiAccessLogCreate(ApiAccessLogBase):
    """创建API访问日志Schema"""
    pass


class ApiAccessLog(ApiAccessLogBase, TimestampMixin, BaseSchema):
    """API访问日志响应Schema"""
    id: int


# 系统日志Schema
class SystemLogBase(BaseModel):
    """系统日志基础Schema"""
    level: str  # DEBUG/INFO/WARN/ERROR
    module: str
    message: str
    detail: Optional[str] = None
    trace_id: Optional[str] = None
    app_id: Optional[str] = None
    user_id: Optional[int] = None
    ip_address: Optional[str] = None
    exception: Optional[str] = None
    stack_trace: Optional[str] = None


class SystemLogCreate(SystemLogBase):
    """创建系统日志Schema"""
    pass


class SystemLog(SystemLogBase, TimestampMixin, BaseSchema):
    """系统日志响应Schema"""
    id: int


# 日志查询参数Schema
class LogQueryParams(BaseModel):
    """日志查询参数Schema"""
    app_id: Optional[str] = None
    user_id: Optional[int] = None
    level: Optional[str] = None
    module: Optional[str] = None
    action: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    keyword: Optional[str] = None
    page: int = 1
    page_size: int = 20


# 日志统计Schema
class LogStats(BaseModel):
    """日志统计Schema"""
    total_count: int
    error_count: int
    warn_count: int
    info_count: int
    debug_count: int
    today_count: int
    yesterday_count: int
    week_count: int
    month_count: int
