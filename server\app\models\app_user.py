"""
应用用户模型
"""
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class AppUser(BaseModel):
    """应用用户表"""
    __tablename__ = "app_users"
    
    app_id = Column(String(50), ForeignKey("applications.app_id"), nullable=False, index=True, comment="所属应用ID")
    username = Column(String(50), nullable=False, index=True, comment="用户名")
    password_hash = Column(String(255), nullable=True, comment="密码哈希")
    phone = Column(String(20), nullable=True, index=True, comment="手机号")
    email = Column(String(100), nullable=True, index=True, comment="邮箱")
    nickname = Column(String(50), nullable=True, comment="昵称")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    gender = Column(Integer, nullable=True, comment="性别：1-男，2-女，0-未知")
    birthday = Column(String(20), nullable=True, comment="生日")
    status = Column(Integer, default=1, nullable=False, comment="状态：1-正常，0-禁用")
    register_type = Column(String(20), nullable=True, comment="注册方式：phone/email/wechat")
    register_ip = Column(String(50), nullable=True, comment="注册IP")
    last_login_at = Column(DateTime, nullable=True, comment="最后登录时间")
    last_login_ip = Column(String(50), nullable=True, comment="最后登录IP")
    login_fail_count = Column(Integer, default=0, nullable=False, comment="连续登录失败次数")
    locked_until = Column(DateTime, nullable=True, comment="锁定截止时间")
    ext_info = Column(Text, nullable=True, comment="扩展信息JSON")
    
    # 关联关系
    application = relationship("Application", back_populates="users")
    login_history = relationship("UserLoginHistory", back_populates="user")


class UserLoginHistory(BaseModel):
    """用户登录历史表"""
    __tablename__ = "user_login_history"
    
    user_id = Column(Integer, ForeignKey("app_users.id"), nullable=False, index=True, comment="用户ID")
    app_id = Column(String(50), ForeignKey("applications.app_id"), nullable=False, index=True, comment="应用ID")
    login_ip = Column(String(50), nullable=True, comment="登录IP")
    user_agent = Column(Text, nullable=True, comment="用户代理信息")
    login_type = Column(String(20), nullable=True, comment="登录方式")
    status = Column(Integer, nullable=False, comment="登录结果：1-成功，0-失败")
    fail_reason = Column(String(200), nullable=True, comment="失败原因")
    location = Column(String(100), nullable=True, comment="登录地点")
    device_info = Column(Text, nullable=True, comment="设备信息JSON")
    
    # 关联关系
    user = relationship("AppUser", back_populates="login_history")
