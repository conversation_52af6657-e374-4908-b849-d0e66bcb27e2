"""
管理员用户模型
"""
from sqlalchemy import Column, Integer, String, Text, ForeignKey
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class AdminUser(BaseModel):
    """管理员用户表"""
    __tablename__ = "admin_users"
    
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    email = Column(String(100), unique=True, index=True, nullable=True, comment="邮箱")
    real_name = Column(String(50), nullable=True, comment="真实姓名")
    phone = Column(String(20), nullable=True, comment="手机号")
    status = Column(Integer, default=1, nullable=False, comment="状态：1-正常，0-禁用")
    role_id = Column(Integer, ForeignKey("admin_roles.id"), nullable=True, comment="角色ID")
    last_login_at = Column(String(50), nullable=True, comment="最后登录时间")
    login_fail_count = Column(Integer, default=0, nullable=False, comment="连续登录失败次数")
    locked_until = Column(String(50), nullable=True, comment="锁定截止时间")
    remark = Column(Text, nullable=True, comment="备注")
    
    # 关联关系
    role = relationship("AdminRole", back_populates="users")


class AdminRole(BaseModel):
    """管理员角色表"""
    __tablename__ = "admin_roles"
    
    name = Column(String(50), unique=True, nullable=False, comment="角色名称")
    code = Column(String(50), unique=True, nullable=False, comment="角色代码")
    description = Column(Text, nullable=True, comment="角色描述")
    status = Column(Integer, default=1, nullable=False, comment="状态：1-正常，0-禁用")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序")
    
    # 关联关系
    users = relationship("AdminUser", back_populates="role")
    menus = relationship("AdminRoleMenu", back_populates="role")


class AdminMenu(BaseModel):
    """管理员菜单表"""
    __tablename__ = "admin_menus"
    
    name = Column(String(50), nullable=False, comment="菜单名称")
    code = Column(String(50), unique=True, nullable=False, comment="菜单代码")
    path = Column(String(200), nullable=True, comment="路由路径")
    component = Column(String(200), nullable=True, comment="组件路径")
    icon = Column(String(50), nullable=True, comment="图标")
    parent_id = Column(Integer, ForeignKey("admin_menus.id"), nullable=True, comment="父菜单ID")
    menu_type = Column(Integer, default=1, nullable=False, comment="菜单类型：1-菜单，2-按钮")
    status = Column(Integer, default=1, nullable=False, comment="状态：1-正常，0-禁用")
    sort_order = Column(Integer, default=0, nullable=False, comment="排序")
    permission = Column(String(100), nullable=True, comment="权限标识")
    
    # 关联关系
    parent = relationship("AdminMenu", remote_side=[id])
    roles = relationship("AdminRoleMenu", back_populates="menu")


class AdminRoleMenu(BaseModel):
    """管理员角色菜单关联表"""
    __tablename__ = "admin_role_menus"
    
    role_id = Column(Integer, ForeignKey("admin_roles.id"), nullable=False, comment="角色ID")
    menu_id = Column(Integer, ForeignKey("admin_menus.id"), nullable=False, comment="菜单ID")
    
    # 关联关系
    role = relationship("AdminRole", back_populates="menus")
    menu = relationship("AdminMenu", back_populates="roles")
