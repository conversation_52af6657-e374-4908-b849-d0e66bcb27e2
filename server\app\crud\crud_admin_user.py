"""
管理员用户CRUD操作
"""
from typing import Any, Dict, Optional, Union, List

from sqlalchemy.orm import Session

from app.core.security import get_password_hash, verify_password
from app.crud.base import CRUDBase
from app.models.admin_user import Admin<PERSON>ser, AdminRole, AdminMenu, AdminRoleMenu
from app.schemas.admin_user import AdminUserCreate, AdminUserUpdate


class CRUDAdminUser(CRUDBase[AdminUser, AdminUserCreate, AdminUserUpdate]):
    """管理员用户CRUD"""
    
    def get_by_username(self, db: Session, *, username: str) -> Optional[AdminUser]:
        """根据用户名获取管理员"""
        return db.query(AdminUser).filter(AdminUser.username == username).first()

    def get_by_email(self, db: Session, *, email: str) -> Optional[AdminUser]:
        """根据邮箱获取管理员"""
        return db.query(AdminUser).filter(AdminUser.email == email).first()

    def create(self, db: Session, *, obj_in: AdminUserCreate) -> AdminUser:
        """创建管理员用户"""
        db_obj = AdminUser(
            username=obj_in.username,
            email=obj_in.email,
            password_hash=get_password_hash(obj_in.password),
            real_name=obj_in.real_name,
            phone=obj_in.phone,
            status=obj_in.status,
            role_id=obj_in.role_id,
            remark=obj_in.remark,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self, db: Session, *, db_obj: AdminUser, obj_in: Union[AdminUserUpdate, Dict[str, Any]]
    ) -> AdminUser:
        """更新管理员用户"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def authenticate(self, db: Session, *, username: str, password: str) -> Optional[AdminUser]:
        """验证管理员用户"""
        user = self.get_by_username(db, username=username)
        if not user:
            return None
        if not verify_password(password, user.password_hash):
            return None
        return user

    def is_active(self, user: AdminUser) -> bool:
        """检查用户是否活跃"""
        return user.status == 1

    def is_superuser(self, user: AdminUser) -> bool:
        """检查是否为超级用户"""
        # 这里可以根据角色或其他字段判断
        return user.role_id == 1  # 假设role_id=1为超级管理员

    def update_password(self, db: Session, *, user: AdminUser, new_password: str) -> AdminUser:
        """更新密码"""
        user.password_hash = get_password_hash(new_password)
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def increment_login_fail_count(self, db: Session, *, user: AdminUser) -> AdminUser:
        """增加登录失败次数"""
        user.login_fail_count += 1
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def reset_login_fail_count(self, db: Session, *, user: AdminUser) -> AdminUser:
        """重置登录失败次数"""
        user.login_fail_count = 0
        user.locked_until = None
        db.add(user)
        db.commit()
        db.refresh(user)
        return user


class CRUDAdminRole(CRUDBase[AdminRole, Dict, Dict]):
    """管理员角色CRUD"""
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[AdminRole]:
        """根据角色代码获取角色"""
        return db.query(AdminRole).filter(AdminRole.code == code).first()

    def get_roles_with_menus(self, db: Session, *, role_id: int) -> Optional[AdminRole]:
        """获取角色及其菜单"""
        return db.query(AdminRole).filter(AdminRole.id == role_id).first()


class CRUDAdminMenu(CRUDBase[AdminMenu, Dict, Dict]):
    """管理员菜单CRUD"""
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[AdminMenu]:
        """根据菜单代码获取菜单"""
        return db.query(AdminMenu).filter(AdminMenu.code == code).first()

    def get_menu_tree(self, db: Session) -> List[AdminMenu]:
        """获取菜单树"""
        return db.query(AdminMenu).filter(AdminMenu.parent_id.is_(None)).all()

    def get_user_menus(self, db: Session, *, user_id: int) -> List[AdminMenu]:
        """获取用户菜单"""
        user = db.query(AdminUser).filter(AdminUser.id == user_id).first()
        if not user or not user.role_id:
            return []
        
        menu_ids = db.query(AdminRoleMenu.menu_id).filter(
            AdminRoleMenu.role_id == user.role_id
        ).all()
        menu_ids = [mid[0] for mid in menu_ids]
        
        return db.query(AdminMenu).filter(
            AdminMenu.id.in_(menu_ids),
            AdminMenu.status == 1
        ).order_by(AdminMenu.sort_order).all()


# 创建CRUD实例
admin_user = CRUDAdminUser(AdminUser)
admin_role = CRUDAdminRole(AdminRole)
admin_menu = CRUDAdminMenu(AdminMenu)
