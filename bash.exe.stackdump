Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA650) msys-2.0.dll+0x1FE8E
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210286019, 0007FFFFB608, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB750  000210068E24 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA30  00021006A225 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8DF820000 ntdll.dll
7FF8DEC90000 KERNEL32.DLL
7FF8DD1B0000 KERNELBASE.dll
7FF8DDE50000 USER32.dll
7FF8DCDD0000 win32u.dll
7FF8DF240000 GDI32.dll
7FF8DD090000 gdi32full.dll
7FF8DCE00000 msvcp_win.dll
7FF8DCCB0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8DEF00000 advapi32.dll
7FF8DEBE0000 msvcrt.dll
7FF8DE220000 sechost.dll
7FF8DE000000 RPCRT4.dll
7FF8DC320000 CRYPTBASE.DLL
7FF8DD010000 bcryptPrimitives.dll
7FF8DD680000 IMM32.DLL
