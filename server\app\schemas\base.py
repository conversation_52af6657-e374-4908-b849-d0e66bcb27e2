"""
基础Schema定义
"""
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict


class BaseSchema(BaseModel):
    """基础Schema类"""
    model_config = ConfigDict(from_attributes=True)


class TimestampMixin(BaseModel):
    """时间戳混入类"""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ResponseBase(BaseModel):
    """基础响应模型"""
    code: int = 200
    message: str = "success"
    data: Optional[dict] = None


class PaginationParams(BaseModel):
    """分页参数"""
    page: int = 1
    page_size: int = 20
    
    def get_offset(self) -> int:
        """获取偏移量"""
        return (self.page - 1) * self.page_size


class PaginationResponse(BaseModel):
    """分页响应"""
    total: int
    page: int
    page_size: int
    pages: int
    items: list
