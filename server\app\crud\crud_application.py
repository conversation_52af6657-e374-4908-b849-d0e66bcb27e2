"""
应用CRUD操作
"""
import secrets
from typing import Any, Dict, Optional, Union, List

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.application import Application
from app.schemas.application import ApplicationCreate, ApplicationUpdate


class CRUDApplication(CRUDBase[Application, ApplicationCreate, ApplicationUpdate]):
    """应用CRUD"""
    
    def get_by_app_id(self, db: Session, *, app_id: str) -> Optional[Application]:
        """根据app_id获取应用"""
        return db.query(Application).filter(Application.app_id == app_id).first()

    def get_by_name(self, db: Session, *, name: str) -> Optional[Application]:
        """根据应用名称获取应用"""
        return db.query(Application).filter(Application.name == name).first()

    def create(self, db: Session, *, obj_in: ApplicationCreate) -> Application:
        """创建应用"""
        # 生成唯一的app_id和app_secret
        app_id = self._generate_app_id()
        app_secret = self._generate_app_secret()
        
        # 确保app_id唯一
        while self.get_by_app_id(db, app_id=app_id):
            app_id = self._generate_app_id()
        
        db_obj = Application(
            name=obj_in.name,
            app_id=app_id,
            app_secret=app_secret,
            app_type=obj_in.app_type,
            description=obj_in.description,
            status=obj_in.status,
            domain=obj_in.domain,
            callback_url=obj_in.callback_url,
            logo_url=obj_in.logo_url,
            contact_name=obj_in.contact_name,
            contact_phone=obj_in.contact_phone,
            contact_email=obj_in.contact_email,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self, 
        db: Session, 
        *, 
        db_obj: Application, 
        obj_in: Union[ApplicationUpdate, Dict[str, Any]]
    ) -> Application:
        """更新应用"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        return super().update(db, db_obj=db_obj, obj_in=update_data)

    def get_active_apps(self, db: Session) -> List[Application]:
        """获取所有活跃的应用"""
        return db.query(Application).filter(Application.status == 1).all()

    def get_apps_by_type(self, db: Session, *, app_type: str) -> List[Application]:
        """根据应用类型获取应用"""
        return db.query(Application).filter(Application.app_type == app_type).all()

    def is_active(self, app: Application) -> bool:
        """检查应用是否活跃"""
        return app.status == 1

    def regenerate_secret(self, db: Session, *, app: Application) -> Application:
        """重新生成应用密钥"""
        app.app_secret = self._generate_app_secret()
        db.add(app)
        db.commit()
        db.refresh(app)
        return app

    def _generate_app_id(self) -> str:
        """生成应用ID"""
        # 生成格式：APP_YYYYMMDD_XXXXXX (6位随机字符)
        import datetime
        date_str = datetime.datetime.now().strftime("%Y%m%d")
        random_str = secrets.token_hex(3).upper()  # 6位十六进制字符
        return f"APP_{date_str}_{random_str}"

    def _generate_app_secret(self) -> str:
        """生成应用密钥"""
        return secrets.token_urlsafe(32)

    def verify_app_secret(self, app: Application, secret: str) -> bool:
        """验证应用密钥"""
        return app.app_secret == secret

    def get_app_stats(self, db: Session, *, app_id: str) -> Dict[str, Any]:
        """获取应用统计信息"""
        from app.models.app_user import AppUser
        from app.models.log import ApiAccessLog
        from datetime import datetime, timedelta
        
        app = self.get_by_app_id(db, app_id=app_id)
        if not app:
            return {}
        
        # 用户统计
        total_users = db.query(AppUser).filter(AppUser.app_id == app_id).count()
        
        # 今天活跃用户
        today = datetime.now().date()
        active_users_today = db.query(AppUser).filter(
            AppUser.app_id == app_id,
            AppUser.last_login_at >= today
        ).count()
        
        # API调用统计
        api_calls_today = db.query(ApiAccessLog).filter(
            ApiAccessLog.app_id == app_id,
            ApiAccessLog.created_at >= today
        ).count()
        
        # 错误率统计
        error_calls_today = db.query(ApiAccessLog).filter(
            ApiAccessLog.app_id == app_id,
            ApiAccessLog.created_at >= today,
            ApiAccessLog.status_code >= 400
        ).count()
        
        error_rate = (error_calls_today / api_calls_today * 100) if api_calls_today > 0 else 0
        
        return {
            "app_id": app_id,
            "app_name": app.name,
            "total_users": total_users,
            "active_users_today": active_users_today,
            "api_calls_today": api_calls_today,
            "error_rate_today": round(error_rate, 2)
        }


# 创建CRUD实例
application = CRUDApplication(Application)
