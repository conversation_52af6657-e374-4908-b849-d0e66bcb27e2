"""
基础CRUD类
"""
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.db.base import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """基础CRUD类"""
    
    def __init__(self, model: Type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).
        
        **Parameters**
        
        * `model`: A SQLAlchemy model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model = model

    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        return db.query(self.model).filter(self.model.id == id).first()

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """获取多个记录"""
        return db.query(self.model).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        """创建记录"""
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data)  # type: ignore
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """更新记录"""
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int) -> ModelType:
        """删除记录"""
        obj = db.query(self.model).get(id)
        db.delete(obj)
        db.commit()
        return obj


class CRUDBaseWithApp(CRUDBase[ModelType, CreateSchemaType, UpdateSchemaType]):
    """带应用隔离的基础CRUD类"""
    
    def get_by_app(self, db: Session, *, app_id: str, id: Any) -> Optional[ModelType]:
        """根据应用ID和记录ID获取单个记录"""
        return db.query(self.model).filter(
            self.model.app_id == app_id,
            self.model.id == id
        ).first()

    def get_multi_by_app(
        self, 
        db: Session, 
        *, 
        app_id: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[ModelType]:
        """根据应用ID获取多个记录"""
        return db.query(self.model).filter(
            self.model.app_id == app_id
        ).offset(skip).limit(limit).all()

    def create_with_app(
        self, 
        db: Session, 
        *, 
        obj_in: CreateSchemaType, 
        app_id: str
    ) -> ModelType:
        """创建带应用ID的记录"""
        obj_in_data = jsonable_encoder(obj_in)
        obj_in_data["app_id"] = app_id
        db_obj = self.model(**obj_in_data)  # type: ignore
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_by_app(
        self,
        db: Session,
        *,
        app_id: str,
        id: Any,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> Optional[ModelType]:
        """根据应用ID更新记录"""
        db_obj = self.get_by_app(db, app_id=app_id, id=id)
        if not db_obj:
            return None
        return self.update(db, db_obj=db_obj, obj_in=obj_in)

    def remove_by_app(self, db: Session, *, app_id: str, id: int) -> Optional[ModelType]:
        """根据应用ID删除记录"""
        obj = db.query(self.model).filter(
            self.model.app_id == app_id,
            self.model.id == id
        ).first()
        if obj:
            db.delete(obj)
            db.commit()
        return obj

    def count_by_app(self, db: Session, *, app_id: str) -> int:
        """根据应用ID统计记录数"""
        return db.query(self.model).filter(self.model.app_id == app_id).count()
