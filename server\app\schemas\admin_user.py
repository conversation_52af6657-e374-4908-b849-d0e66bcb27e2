"""
管理员用户Schema
"""
from typing import Optional, List
from pydantic import BaseModel, EmailStr

from app.schemas.base import BaseSchema, TimestampMixin


# 管理员用户Schema
class AdminUserBase(BaseModel):
    """管理员用户基础Schema"""
    username: str
    email: Optional[EmailStr] = None
    real_name: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[int] = 1
    role_id: Optional[int] = None
    remark: Optional[str] = None


class AdminUserCreate(AdminUserBase):
    """创建管理员用户Schema"""
    password: str


class AdminUserUpdate(BaseModel):
    """更新管理员用户Schema"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    real_name: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[int] = None
    role_id: Optional[int] = None
    remark: Optional[str] = None


class AdminUserInDB(AdminUserBase, TimestampMixin, BaseSchema):
    """数据库中的管理员用户Schema"""
    id: int
    password_hash: str
    last_login_at: Optional[str] = None
    login_fail_count: int = 0
    locked_until: Optional[str] = None


class AdminUser(AdminUserBase, TimestampMixin, BaseSchema):
    """管理员用户响应Schema"""
    id: int
    last_login_at: Optional[str] = None
    login_fail_count: int = 0
    locked_until: Optional[str] = None


# 管理员角色Schema
class AdminRoleBase(BaseModel):
    """管理员角色基础Schema"""
    name: str
    code: str
    description: Optional[str] = None
    status: Optional[int] = 1
    sort_order: Optional[int] = 0


class AdminRoleCreate(AdminRoleBase):
    """创建管理员角色Schema"""
    pass


class AdminRoleUpdate(BaseModel):
    """更新管理员角色Schema"""
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    status: Optional[int] = None
    sort_order: Optional[int] = None


class AdminRole(AdminRoleBase, TimestampMixin, BaseSchema):
    """管理员角色响应Schema"""
    id: int


# 管理员菜单Schema
class AdminMenuBase(BaseModel):
    """管理员菜单基础Schema"""
    name: str
    code: str
    path: Optional[str] = None
    component: Optional[str] = None
    icon: Optional[str] = None
    parent_id: Optional[int] = None
    menu_type: Optional[int] = 1
    status: Optional[int] = 1
    sort_order: Optional[int] = 0
    permission: Optional[str] = None


class AdminMenuCreate(AdminMenuBase):
    """创建管理员菜单Schema"""
    pass


class AdminMenuUpdate(BaseModel):
    """更新管理员菜单Schema"""
    name: Optional[str] = None
    code: Optional[str] = None
    path: Optional[str] = None
    component: Optional[str] = None
    icon: Optional[str] = None
    parent_id: Optional[int] = None
    menu_type: Optional[int] = None
    status: Optional[int] = None
    sort_order: Optional[int] = None
    permission: Optional[str] = None


class AdminMenu(AdminMenuBase, TimestampMixin, BaseSchema):
    """管理员菜单响应Schema"""
    id: int
    children: Optional[List['AdminMenu']] = []


# 登录相关Schema
class AdminLoginRequest(BaseModel):
    """管理员登录请求Schema"""
    username: str
    password: str
    captcha: Optional[str] = None


class AdminLoginResponse(BaseModel):
    """管理员登录响应Schema"""
    access_token: str
    token_type: str = "bearer"
    user_info: AdminUser


class AdminChangePasswordRequest(BaseModel):
    """管理员修改密码请求Schema"""
    old_password: str
    new_password: str
