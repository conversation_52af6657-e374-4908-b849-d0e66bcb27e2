"""
日志相关模型
"""
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class OperationLog(BaseModel):
    """操作日志表"""
    __tablename__ = "operation_logs"
    
    user_id = Column(Integer, nullable=False, index=True, comment="操作人ID")
    user_type = Column(String(20), nullable=False, comment="用户类型：admin/app_user")
    app_id = Column(String(50), nullable=True, index=True, comment="应用ID（应用用户操作时必填）")
    action = Column(String(50), nullable=False, comment="操作类型")
    target_type = Column(String(50), nullable=True, comment="目标类型")
    target_id = Column(String(50), nullable=True, comment="目标ID")
    target_name = Column(String(200), nullable=True, comment="目标名称")
    detail = Column(Text, nullable=True, comment="操作详情JSON")
    ip_address = Column(String(50), nullable=True, comment="操作IP")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    status = Column(Integer, nullable=False, comment="操作结果：1-成功，0-失败")
    error_message = Column(Text, nullable=True, comment="错误信息")
    duration = Column(Integer, nullable=True, comment="操作耗时（毫秒）")


class ApiAccessLog(BaseModel):
    """API访问日志表"""
    __tablename__ = "api_access_logs"
    
    app_id = Column(String(50), ForeignKey("applications.app_id"), nullable=True, index=True, comment="应用ID")
    user_id = Column(Integer, nullable=True, index=True, comment="用户ID")
    user_type = Column(String(20), nullable=True, comment="用户类型：admin/app_user")
    request_id = Column(String(50), nullable=True, index=True, comment="请求ID")
    method = Column(String(10), nullable=False, comment="请求方法")
    path = Column(String(500), nullable=False, comment="请求路径")
    query_params = Column(Text, nullable=True, comment="查询参数JSON")
    request_body = Column(Text, nullable=True, comment="请求体")
    response_body = Column(Text, nullable=True, comment="响应体")
    status_code = Column(Integer, nullable=False, comment="HTTP状态码")
    latency = Column(Integer, nullable=False, comment="响应耗时（毫秒）")
    ip_address = Column(String(50), nullable=True, comment="请求IP")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    referer = Column(String(500), nullable=True, comment="来源页面")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 关联关系
    application = relationship("Application", back_populates="logs")


class SystemLog(BaseModel):
    """系统日志表"""
    __tablename__ = "system_logs"
    
    level = Column(String(10), nullable=False, index=True, comment="日志级别：DEBUG/INFO/WARN/ERROR")
    module = Column(String(50), nullable=False, index=True, comment="模块名称")
    message = Column(Text, nullable=False, comment="日志消息")
    detail = Column(Text, nullable=True, comment="详细信息JSON")
    trace_id = Column(String(50), nullable=True, index=True, comment="追踪ID")
    app_id = Column(String(50), nullable=True, index=True, comment="应用ID")
    user_id = Column(Integer, nullable=True, comment="用户ID")
    ip_address = Column(String(50), nullable=True, comment="IP地址")
    exception = Column(Text, nullable=True, comment="异常信息")
    stack_trace = Column(Text, nullable=True, comment="堆栈信息")
