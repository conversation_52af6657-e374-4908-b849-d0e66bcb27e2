"""
应用用户Schema
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr

from app.schemas.base import BaseSchema, TimestampMixin


class AppUserBase(BaseModel):
    """应用用户基础Schema"""
    username: str
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[int] = None
    birthday: Optional[str] = None
    status: Optional[int] = 1
    register_type: Optional[str] = None


class AppUserCreate(AppUserBase):
    """创建应用用户Schema"""
    app_id: str  # 必须包含app_id
    password: Optional[str] = None


class AppUserUpdate(BaseModel):
    """更新应用用户Schema"""
    username: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[int] = None
    birthday: Optional[str] = None
    status: Optional[int] = None


class AppUserInDB(AppUserBase, TimestampMixin, BaseSchema):
    """数据库中的应用用户Schema"""
    id: int
    app_id: str
    password_hash: Optional[str] = None
    register_ip: Optional[str] = None
    last_login_at: Optional[datetime] = None
    last_login_ip: Optional[str] = None
    login_fail_count: int = 0
    locked_until: Optional[datetime] = None
    ext_info: Optional[str] = None


class AppUser(AppUserBase, TimestampMixin, BaseSchema):
    """应用用户响应Schema"""
    id: int
    app_id: str
    register_ip: Optional[str] = None
    last_login_at: Optional[datetime] = None
    last_login_ip: Optional[str] = None
    login_fail_count: int = 0
    locked_until: Optional[datetime] = None


# 应用用户登录相关Schema
class AppUserLoginRequest(BaseModel):
    """应用用户登录请求Schema"""
    username: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    password: str
    login_type: Optional[str] = "password"  # password/sms/email


class AppUserRegisterRequest(BaseModel):
    """应用用户注册请求Schema"""
    username: str
    password: str
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    nickname: Optional[str] = None
    register_type: Optional[str] = "username"


class AppUserLoginResponse(BaseModel):
    """应用用户登录响应Schema"""
    access_token: str
    token_type: str = "bearer"
    user_info: AppUser


class AppUserChangePasswordRequest(BaseModel):
    """应用用户修改密码请求Schema"""
    old_password: str
    new_password: str


class AppUserResetPasswordRequest(BaseModel):
    """应用用户重置密码请求Schema"""
    username: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    verification_code: str
    new_password: str


# 用户登录历史Schema
class UserLoginHistoryBase(BaseModel):
    """用户登录历史基础Schema"""
    login_ip: Optional[str] = None
    user_agent: Optional[str] = None
    login_type: Optional[str] = None
    status: int
    fail_reason: Optional[str] = None
    location: Optional[str] = None


class UserLoginHistory(UserLoginHistoryBase, TimestampMixin, BaseSchema):
    """用户登录历史响应Schema"""
    id: int
    user_id: int
    app_id: str
