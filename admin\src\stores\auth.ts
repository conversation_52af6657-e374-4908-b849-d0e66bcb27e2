import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import { login, logout, getUserInfo } from '@/api/auth'
import type { LoginForm, AdminUser } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>(Cookies.get('token') || '')
  const userInfo = ref<AdminUser | null>(null)
  const loading = ref(false)

  const isLoggedIn = computed(() => !!token.value)

  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    loading.value = true
    try {
      const response = await login(loginForm)
      token.value = response.access_token
      userInfo.value = response.user_info
      
      // 保存token到cookie
      Cookies.set('token', response.access_token, { expires: 7 })
      
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      token.value = ''
      userInfo.value = null
      Cookies.remove('token')
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    if (!token.value) return null
    
    try {
      const response = await getUserInfo()
      userInfo.value = response
      return response
    } catch (error) {
      // 如果获取用户信息失败，清除token
      token.value = ''
      userInfo.value = null
      Cookies.remove('token')
      throw error
    }
  }

  // 初始化用户信息
  const initUserInfo = async () => {
    if (token.value && !userInfo.value) {
      try {
        await getUserInfoAction()
      } catch (error) {
        console.error('Init user info error:', error)
      }
    }
  }

  return {
    token,
    userInfo,
    loading,
    isLoggedIn,
    loginAction,
    logoutAction,
    getUserInfoAction,
    initUserInfo
  }
})
