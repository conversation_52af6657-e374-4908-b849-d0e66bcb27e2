"""
应用端API路由
"""
from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud, schemas
from app.api.v1.deps import (
    get_db,
    get_current_app
)
from app.core import security
from app.core.config import settings
from app.models.application import Application

router = APIRouter()


@router.post("/auth/register", response_model=schemas.AppUserLoginResponse)
def register_app_user(
    *,
    db: Session = Depends(get_db),
    user_in: schemas.AppUserRegisterRequest,
    current_app: Application = Depends(get_current_app)
) -> Any:
    """应用用户注册"""
    # 检查用户名是否已存在
    existing_user = crud.app_user.get_by_username(
        db, app_id=current_app.app_id, username=user_in.username
    )
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    # 检查手机号是否已存在
    if user_in.phone:
        existing_phone = crud.app_user.get_by_phone(
            db, app_id=current_app.app_id, phone=user_in.phone
        )
        if existing_phone:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号已存在"
            )

    # 检查邮箱是否已存在
    if user_in.email:
        existing_email = crud.app_user.get_by_email(
            db, app_id=current_app.app_id, email=user_in.email
        )
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )

    # 创建用户
    user_create = schemas.AppUserCreate(
        app_id=current_app.app_id,
        username=user_in.username,
        password=user_in.password,
        phone=user_in.phone,
        email=user_in.email,
        nickname=user_in.nickname,
        register_type=user_in.register_type
    )
    user = crud.app_user.create_with_app(db, obj_in=user_create, app_id=current_app.app_id)

    # 生成访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_app_access_token(
        user.id, current_app.app_id, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_info": user
    }


@router.post("/auth/login", response_model=schemas.AppUserLoginResponse)
def login_app_user(
    *,
    db: Session = Depends(get_db),
    user_in: schemas.AppUserLoginRequest,
    current_app: Application = Depends(get_current_app)
) -> Any:
    """应用用户登录"""
    user = crud.app_user.authenticate(
        db,
        app_id=current_app.app_id,
        username=user_in.username,
        phone=user_in.phone,
        email=user_in.email,
        password=user_in.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    elif not crud.app_user.is_active(user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )

    # 更新最后登录时间
    crud.app_user.update_last_login(db, user=user)

    # 生成访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_app_access_token(
        user.id, current_app.app_id, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_info": user
    }


@router.get("/")
async def apps_root(
    current_app: Application = Depends(get_current_app)
):
    """应用端根路径"""
    return {
        "message": "应用端API",
        "app_name": current_app.name,
        "app_id": current_app.app_id
    }


@router.get("/health")
async def apps_health(
    current_app: Application = Depends(get_current_app)
):
    """应用端健康检查"""
    return {
        "status": "healthy",
        "module": "apps",
        "app_id": current_app.app_id
    }
