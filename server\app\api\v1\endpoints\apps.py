"""
应用端API路由
"""
from fastapi import APIRouter, Depends

from app.api.v1.deps import get_current_app
from app.models.application import Application

router = APIRouter()


@router.get("/")
async def apps_root(
    current_app: Application = Depends(get_current_app)
):
    """应用端根路径"""
    return {
        "message": "应用端API",
        "app_name": current_app.name,
        "app_id": current_app.app_id
    }


@router.get("/health")
async def apps_health(
    current_app: Application = Depends(get_current_app)
):
    """应用端健康检查"""
    return {
        "status": "healthy", 
        "module": "apps",
        "app_id": current_app.app_id
    }
