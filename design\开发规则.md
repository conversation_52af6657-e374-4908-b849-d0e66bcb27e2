# 多应用管理与监控平台 - 开发规则与代码规范

本文档统一 server/admin/apps 项目结构下的开发行为，确保代码风格一致、版本管理清晰、协作高效。所有项目参与者都应遵循以下规则。

---

## 1. 目录与命名规范

- **根目录结构**：
  - `server/`：后端服务，统一支撑所有应用端。
  - `admin/`：管理后台前端。
  - `apps/`：各类应用端（如 h5/、mini/、app/ 等）。
  - `design/`：设计与规范文档。
- **命名**：
  - 目录、文件、变量、接口等命名需清晰、见名知意。
  - 多应用相关的表、接口、Schema 必须包含 `app_id` 字段。

---

## 2. Git 版本管理规范

- 采用简化的 GitHub Flow：
  - `main`：主分支。
  - `feature/<feature-name>`：新功能开发分支。
- 分支、提交信息、PR 流程同前述规范。

---

## 3. 后端（server）开发规范

### 3.1 核心原则：多租户隔离
- **强制隔离**: 所有与业务相关的数据表、接口、Schema、日志、配置等，都必须能够基于 `app_id` 进行严格隔离。这是系统安全的基石。

### 3.2 API 设计与安全
- **路由前缀**:
    - 管理员 API: `/api/v1/admin/...`
    - 应用端 API: `/api/v1/apps/...`
- **应用标识**:
    - **必须** 使用 HTTP Header `X-App-ID` 传递应用标识。
    - **禁止** 在 URL 路径或查询参数中传递 `app_id`。
- **依赖注入**:
    - 应用端 API **必须** 使用 `Depends(get_current_app)` 依赖来完成应用验证和实例获取。
    - 用户认证相关的 API **必须** 使用 `Depends(get_current_user)` 类似的依赖来获取当前用户。
- **JWT 规范**:
    - **管理员JWT**: 签发给管理员，Claims 中应包含 `user_id` 和 `role`。
    - **应用用户JWT**: 签发给应用端用户，Claims 中 **必须** 包含 `user_id` 和 `app_id`。

### 3.3 代码风格与工具
- **代码风格**: Python 代码遵循 PEP8，使用 `black` (格式化), `isort` (排序), `ruff` (Linter)。
- **数据库迁移**: 始终使用 `Alembic` 管理数据库 Schema 变更，严禁手动修改生产数据库。

---

## 4. 前端（admin & apps）开发规范

- **admin/**：管理后台，主要面向管理员，需实现应用管理、用户管理、配置、监控等功能。
- **apps/**：各应用端独立开发，需通过API与server交互，所有请求需带 `app_id`。
- **代码风格**：Vue3/TS，使用 Prettier/ESLint。
- **组件与状态管理**：合理拆分组件，Pinia 按业务模块组织。

---

## 5. 数据库 CRUD 规范

### 5.1 命名规范
- **表名**: 小写、复数、下划线分隔 (`snake_case`)，如 `users`, `applications`, `app_configs`。
- **字段名**: 小写、单数、下划线分隔 (`snake_case`)，如 `user_name`, `created_at`。
- **主键**: 统一为 `id`。
- **外键**: `{关联表单数}_id`，如 `user_id`, `app_id`。
- **索引**: `idx_{表名}_{字段名}`。

### 5.2 CRUD 函数规范 (强制)
- **函数签名**: 所有针对多租户表（包含 `app_id` 字段）的 CRUD 函数，其签名中 **必须** 包含 `app_id: str` 参数。
    ```python
    # 正确示例
    def get_items_by_app(db: Session, *, app_id: str): ...
    
    # 错误示例
    def get_items(db: Session): ... 
    ```
- **数据过滤**: 在 `get`, `get_multi`, `update`, `delete` 等查询/修改操作中，**必须** 将 `Model.app_id == app_id` 作为数据库查询的 **首要过滤条件**。
    ```python
    # 正确示例
    db.query(Item).filter(Item.app_id == app_id).filter(...)
    
    # 错误示例 - 缺少 app_id 过滤
    db.query(Item).filter(Item.name == "some_name") 
    ```
- **数据创建**: 创建数据时，**必须** 将 `app_id` 显式存入数据库。

---
本规范文档将根据项目需要持续修订。 